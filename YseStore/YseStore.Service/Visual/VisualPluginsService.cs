using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.IService.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 店铺装修插件
    /// </summary>
    public class VisualPluginsService : BaseServices<visual_plugins>, IVisualPluginsService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<VisualPluginsService> _logger;

        public VisualPluginsService(ISqlSugarClient db, ICaching caching, ILogger<VisualPluginsService> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }


        /// <summary>
        /// 读取插件JSON配置
        /// </summary>
        /// <param name="type">插件类型</param>
        /// <param name="mode"></param>
        /// <param name="drafts">插件风格</param>
        /// <returns></returns>
        public async Task<JObject> GetPluginsConfig(string type, string mode, visual_drafts drafts)
        {

            string config =await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/cusvis_mode/{type}/{mode}/config.json"));
            if (!string.IsNullOrEmpty(config))
            {
                //配置
                var configModel = config.JsonToObj<JObject>();

                //themesConfig
                var themesConfig = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/inc/themes.json"));
                if (!themesConfig.IsNullOrEmpty())
                {
                    var themesConfigModel = themesConfig.JsonToObj<JObject>();

                    var pluginsExtConfig = themesConfigModel["PluginsExtConfig"][$"{type}-{mode}"];
                    if (pluginsExtConfig != null && !pluginsExtConfig.IsNullOrEmpty())
                    {
                        var obj = pluginsExtConfig.ToObject<JObject>();

                        configModel = ArrayMerge(configModel, obj);
                    }

                }


                return configModel;

            }

            return null;

        }

        /// <summary>
        /// 递归合并数组，并排除空值
        /// </summary>
        /// <param name="arrs"></param>
        /// <returns></returns>
        public static JObject ArrayMerge(params JObject[] jObjects)
        {
            JObject merged = new JObject();

            foreach (var jObject in jObjects)
            {
                if (jObject == null) continue;

                foreach (var property in jObject)
                {
                    var key = property.Key;
                    var value = property.Value;

                    if (value.Type == JTokenType.Object && merged[key] is JObject mergedObj)
                    {
                        // Recursively merge nested JObject
                        merged[key] = ArrayMerge(mergedObj, (JObject)value);
                    }
                    else
                    {
                        // Directly set or override the value
                        merged[key] = value;
                    }
                }
            }

            return merged;
        }

        /// <summary>
        /// 根据插件ID列表批量获取插件
        /// </summary>
        /// <param name="pluginIds">插件ID列表</param>
        /// <returns></returns>
        public async Task<List<visual_plugins>> GetPluginsByIdsAsync(List<int> pluginIds)
        {
            try
            {
                if (pluginIds == null || !pluginIds.Any())
                {
                    return new List<visual_plugins>();
                }

                var plugins = await db.Queryable<visual_plugins>()
                    .Where(it => pluginIds.Contains(it.PId))
                    .ToListAsync();

                // 处理JSON字段的双引号转义问题
                foreach (var plugin in plugins)
                {
                    plugin.Settings = ProcessJsonField(plugin.Settings);
                    plugin.Blocks = ProcessJsonField(plugin.Blocks);
                    plugin.Config = ProcessJsonField(plugin.Config);
                }

                return plugins;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据插件ID列表批量获取插件失败，PluginIds: {PluginIds}", string.Join(",", pluginIds ?? new List<int>()));
                return new List<visual_plugins>();
            }
        }

        /// <summary>
        /// 处理JSON字段的双引号转义问题
        /// </summary>
        /// <param name="jsonField">原始JSON字段</param>
        /// <returns>处理后的JSON字符串</returns>
        private string ProcessJsonField(string jsonField)
        {
            try
            {
                if (string.IsNullOrEmpty(jsonField))
                {
                    return jsonField;
                }

                var trimmedField = jsonField.Trim();

                // 如果字段已经是有效的JSON对象，尝试验证并返回
                if (trimmedField.StartsWith("{") && trimmedField.EndsWith("}"))
                {
                    try
                    {
                        // 尝试解析，如果成功说明格式正确
                        JsonConvert.DeserializeObject(trimmedField);
                        return trimmedField;
                    }
                    catch
                    {
                        // 如果解析失败，继续下面的处理逻辑
                    }
                }

                // 处理转义的JSON字符串
                // 情况1: 完全转义的JSON字符串 (如: "{\"key\":\"value\"}")
                if (trimmedField.StartsWith("\"") && trimmedField.EndsWith("\"") && trimmedField.Contains("\\\""))
                {
                    // 先反序列化为字符串，再解析为对象，最后重新序列化
                    var unescapedJson = JsonConvert.DeserializeObject<string>(trimmedField);
                    var jsonObject = JsonConvert.DeserializeObject(unescapedJson);
                    return JsonConvert.SerializeObject(jsonObject);
                }

                // 情况2: 部分转义的JSON字符串 (如: {\"key\":\"value\"})
                if (trimmedField.Contains("\\\""))
                {
                    // 直接替换转义字符
                    var fixedJson = trimmedField.Replace("\\\"", "\"");
                    try
                    {
                        var jsonObject = JsonConvert.DeserializeObject(fixedJson);
                        return JsonConvert.SerializeObject(jsonObject);
                    }
                    catch
                    {
                        // 如果修复后仍然无法解析，返回原始值
                        return jsonField;
                    }
                }

                return jsonField;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "处理JSON字段失败，返回原始值: {JsonField}", jsonField?.Substring(0, Math.Min(100, jsonField.Length ?? 0)));
                return jsonField;
            }
        }

    }
}
